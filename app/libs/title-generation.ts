import { azure } from '@ai-sdk/azure'
import { generateText } from 'ai'

/**
 * Generate a conversation title using a lightweight model
 * This runs asynchronously after the first turn to avoid blocking the user
 */
export async function generateTitleWithWeakModel(
  prompt: string
): Promise<string> {
  try {
    const { text } = await generateText({
      model: azure('gpt-4.1-mini'), // Use cheaper, faster model
      maxTokens: 100,
      temperature: 0.3,
      messages: [
        {
          role: 'system',
          content: 'Give a short descriptive title for this conversation',
        },
        {
          role: 'user',
          content: prompt.slice(0, 250), // Limit input to save tokens
        },
      ],
    })

    // Clean up the response
    const cleanTitle = text
      .trim()
      .replace(/^"|"$/g, '') // Remove quotes
      .replace(/\.+$/g, '') // Remove trailing dots
      .slice(0, 50) // Limit length

    return cleanTitle || 'Chat'
  } catch (error) {
    console.error('Failed to generate title:', error)
    return 'Chat' // Fallback title
  }
}
