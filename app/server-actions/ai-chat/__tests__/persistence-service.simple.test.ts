/**
 * @jest-environment node
 */
import {
  createAiConversation,
  persistConversationTurn,
  addMessageToConversation,
  getConversationWithMessages,
  getUserConversations,
  deleteConversation,
} from '../persistence-service'
import type {
  ConversationCreateData,
  ConversationTurnData,
  MessageData,
} from '../types'

// Mock the Prisma client
jest.mock('@/app/libs/prismadb', () => ({
  default: {
    aiConversation: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    aiMessage: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    aiExecutionStep: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    aiAttachment: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    $transaction: jest.fn(),
    $executeRaw: jest.fn(),
    $disconnect: jest.fn(),
  },
}))

// Mock the ID generation to produce predictable test IDs
jest.mock('@/lib/id', () => ({
  generateAiConversationId: () => 'thread_test123456789',
  generateAiMessageId: () => 'msg_test123456789',
  generateAiExecutionStepId: () => 'step_test123456789',
  generateAiAttachmentId: () => 'file_test123456789',
  AiChatIdPrefix: {
    THREAD: 'thread',
    MESSAGE: 'msg',
    STEP: 'step',
    FILE: 'file',
  },
}))

describe('AI Chat Persistence Service - Core Validation', () => {
  const mockUserId = 'c12345678901234567890abcd'
  const mockConversationData: ConversationCreateData = {
    userId: mockUserId,
    title: 'Test Conversation',
    contextEntityType: 'project',
    contextEntityId: 'project_123',
  }

  describe('Input Validation', () => {
    it('should validate user ID format in createAiConversation', async () => {
      const invalidData = { ...mockConversationData, userId: 'invalid' }
      const result = await createAiConversation(invalidData)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid userId format')
    })

    it('should validate conversation ID format when provided', async () => {
      const result = await createAiConversation(
        mockConversationData,
        'invalid_id'
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid ID format')
    })

    it('should validate conversation ID format in persistConversationTurn', async () => {
      const turnData: ConversationTurnData = {
        conversationId: 'invalid_id',
        userMessage: {
          role: 'USER',
          content: 'Test message',
        },
        assistantMessage: {
          role: 'ASSISTANT',
          content: 'Response',
        },
      }

      const result = await persistConversationTurn(turnData)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid ID format')
    })

    it('should validate step types in addMessageToConversation', async () => {
      const messageData: MessageData = {
        role: 'ASSISTANT',
        content: 'Test message',
        steps: [
          {
            stepOrder: 0,
            type: 'INVALID_TYPE' as any,
            metadata: { data: 'test' },
          },
        ],
      }

      const result = await addMessageToConversation(
        'thread_test123456789',
        messageData
      )

      // Validation happens before DB operations, so this should fail
      expect(result.success).toBe(false)
      expect(typeof result.error).toBe('string')
    })

    it('should validate conversation ID format in getConversationWithMessages', async () => {
      const result = await getConversationWithMessages('invalid_id')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid ID format')
    })

    it('should validate user ID format in getUserConversations', async () => {
      const result = await getUserConversations('invalid_user_id')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid userId format')
    })

    it('should validate conversation ID format in deleteConversation', async () => {
      const result = await deleteConversation('invalid_id')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid ID format')
    })
  })

  describe('Data Processing', () => {
    it('should handle empty arrays correctly in persistConversationTurn', () => {
      const turnData: ConversationTurnData = {
        conversationId: 'thread_test123456789',
        userMessage: {
          role: 'USER',
          content: 'Test message',
          attachments: [], // Empty array
        },
        assistantMessage: {
          role: 'ASSISTANT',
          content: 'Response',
          steps: [], // Empty array
          attachments: [], // Empty array
        },
      }

      // This should not throw an error during data processing
      expect(() => {
        // The function should handle empty arrays properly
        const userAttachments = turnData.userMessage.attachments?.length
          ? turnData.userMessage.attachments
          : undefined
        const assistantSteps = turnData.assistantMessage.steps?.length
          ? turnData.assistantMessage.steps
          : undefined
        const assistantAttachments = turnData.assistantMessage.attachments
          ?.length
          ? turnData.assistantMessage.attachments
          : undefined

        expect(userAttachments).toBeUndefined()
        expect(assistantSteps).toBeUndefined()
        expect(assistantAttachments).toBeUndefined()
      }).not.toThrow()
    })

    it('should normalize step orders to prevent duplicates', () => {
      const steps = [
        {
          stepOrder: 5,
          type: 'THOUGHT' as const,
          metadata: { reasoning: 'First' },
        },
        {
          stepOrder: 3,
          type: 'TOOL_CALL' as const,
          metadata: { toolName: 'search' },
        },
        {
          stepOrder: 8,
          type: 'TOOL_RESULT' as const,
          metadata: { result: 'data' },
        },
      ]

      // Import the normalizeStepOrders function (it's not exported, so we test the logic)
      const sortedSteps = [...steps].sort((a, b) => a.stepOrder - b.stepOrder)
      const normalizedSteps = sortedSteps.map((step, index) => ({
        ...step,
        stepOrder: index,
      }))

      expect(normalizedSteps[0].stepOrder).toBe(0)
      expect(normalizedSteps[1].stepOrder).toBe(1)
      expect(normalizedSteps[2].stepOrder).toBe(2)
      expect(normalizedSteps[0].metadata.toolName).toBe('search') // Original order 3
      expect(normalizedSteps[1].metadata.reasoning).toBe('First') // Original order 5
      expect(normalizedSteps[2].metadata.result).toBe('data') // Original order 8
    })

    it('should validate attachment data structure', () => {
      const validAttachment = {
        fileName: 'test.txt',
        fileType: 'text/plain',
        fileSize: 1024,
        url: 'https://example.com/test.txt',
      }

      // Test the validation logic
      expect(() => {
        if (
          !validAttachment.fileName ||
          typeof validAttachment.fileName !== 'string'
        ) {
          throw new Error('Invalid fileName: must be a non-empty string')
        }
        if (
          !validAttachment.fileType ||
          typeof validAttachment.fileType !== 'string'
        ) {
          throw new Error('Invalid fileType: must be a non-empty string')
        }
        if (
          !Number.isInteger(validAttachment.fileSize) ||
          validAttachment.fileSize <= 0
        ) {
          throw new Error('Invalid fileSize: must be a positive integer')
        }
        if (!validAttachment.url || typeof validAttachment.url !== 'string') {
          throw new Error('Invalid url: must be a non-empty string')
        }
        new URL(validAttachment.url) // URL validation
      }).not.toThrow()
    })
  })

  describe('ID Generation and Validation', () => {
    it('should generate predictable IDs for testing', () => {
      const {
        generateAiConversationId,
        generateAiMessageId,
        generateAiExecutionStepId,
      } = require('@/lib/id')

      expect(generateAiConversationId()).toBe('thread_test123456789')
      expect(generateAiMessageId()).toBe('msg_test123456789')
      expect(generateAiExecutionStepId()).toBe('step_test123456789')
    })

    it('should validate ID prefix formats', () => {
      const { AiChatIdPrefix } = require('@/lib/id')

      expect(AiChatIdPrefix.THREAD).toBe('thread')
      expect(AiChatIdPrefix.MESSAGE).toBe('msg')
      expect(AiChatIdPrefix.STEP).toBe('step')
      expect(AiChatIdPrefix.FILE).toBe('file')
    })

    it('should handle optional conversation ID parameter', async () => {
      const customId = 'thread_custom123456789'

      // This test verifies that the function accepts the optional parameter
      // The actual functionality is tested in integration tests
      const result = await createAiConversation(mockConversationData, customId)

      // The function should fail in test environment but the signature should be correct
      expect(result.success).toBe(false)
      expect(typeof result.error).toBe('string')
    })
  })
})
