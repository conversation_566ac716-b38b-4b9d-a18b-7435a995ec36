'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  FiSend,
  FiMessageCircle,
  FiCpu,
  FiSettings,
  FiArrowDown,
  FiCopy,
  FiEdit2,
} from 'react-icons/fi'
import { useAiConversation } from '../../hooks/useAiConversation'
import { useUIStore } from '@/app/stores/ui_store'
// Removed unused imports: BaseTiptapEditor, ReadOnlyTiptapEditor
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { convertTiptapJsonToMarkdown } from '@/app/components/editor/utils'
import { useAiPaneStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { useAssetStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
// Select imports removed (not needed for read-only display)
import { useNavigationStore } from '@/app/stores/navigation_store'
import { InfiniteScrollContainer } from '../chat/InfiniteScrollContainer'
import toast from 'react-hot-toast'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { featureFlags } from '../../lib/feature-flags'
import SimpleMessageList from '../chat/SimpleMessageList'
import SimpleChatInput from '../chat/SimpleChatInput'
import ConversationPerformanceMonitor from '../chat/ConversationPerformanceMonitor'
import { generateAiConversationId } from '@/lib/id'

// Removed unused ExtendedMessage type

type ChatTabContentProps = {
  tab: Tab
  dragTreeId: string
}

const ChatTabContent: React.FC<ChatTabContentProps> = ({ tab, dragTreeId }) => {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [showContextDialog, setShowContextDialog] = useState<boolean>(false)
  const [isInitializing, setIsInitializing] = useState<boolean>(false)
  const [scrollInfo, setScrollInfo] = useState({ isAtBottom: true })
  const [conversationError, setConversationError] = useState<string | null>(
    null
  )
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [showSettingsModal, setShowSettingsModal] = useState<boolean>(false)
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false)
  const [editableTitle, setEditableTitle] = useState<string>(tab.title || '')

  // API endpoint selection
  const useRealLLMAPI = useUIStore(state => state.useRealLLMAPI)
  const apiEndpoint = useRealLLMAPI
    ? '/api/aipane/chat'
    : '/api/aipane/chat-simulator'

  // Get drag tree context
  const nodeContent = useDragTreeStore(state => state.nodeContent)
  const findNodeById = useDragTreeStore(state => state.findNodeById)
  const screeningQuestion = useDragTreeStore(state => state.screeningQuestion)
  const { navigateToTreeNode } = useNavigationStore()

  // Update function to mutate aiPaneData (e.g. clear prompt after first use)
  const updateTabAiPaneData = useTabStore(state => state.updateTabAiPaneData)
  const updateTabTitle = useTabStore(state => state.updateTabTitle)

  // Generate conversation ID immediately for new tabs
  const conversationIdRef = useRef<string>(
    tab.aiPaneData?.conversationId || generateAiConversationId()
  )

  // Update tab store with conversation ID if it's a new tab
  useEffect(() => {
    if (!tab.aiPaneData?.conversationId && conversationIdRef.current) {
      updateTabAiPaneData(tab.id, { conversationId: conversationIdRef.current })
    }
  }, [tab.id, tab.aiPaneData?.conversationId, updateTabAiPaneData])

  // Navigate to context item in outline
  const navigateToContextItem = useCallback(
    (nodeId: string) => {
      navigateToTreeNode(nodeId)
      setShowContextDialog(false)
      console.log('Navigating to node:', nodeId)
    },
    [navigateToTreeNode]
  )

  // Memoize context to prevent unnecessary re-renders
  const contextContent = useCallback(
    () => getContextContent(),
    [tab.aiPaneData?.contextIds, nodeContent]
  )

  // Use conversation ID from tab store or the generated one
  const conversationId =
    tab.aiPaneData?.conversationId || conversationIdRef.current
  const isConversationReady = Boolean(
    conversationId && conversationId.startsWith('thread_')
  )

  // Use the new conversation hook with pagination support
  const conversation = useAiConversation({
    conversationId: isConversationReady ? conversationId : undefined,
    apiEndpoint,
    model: tab.aiPaneData?.model || 'gpt-4.1',
    context: contextContent(),
    settings: tab.aiPaneData?.settings || {},
    contextEntityType: 'drag_tree',
    contextEntityId: dragTreeId,
    contextIds: tab.aiPaneData?.contextIds || [],
    tabId: tab.id,
    initialLimit: 50,
    // Disable the hook until conversation is ready
    enabled: isConversationReady,
  })

  const {
    messages,
    hasMoreMessages,
    isLoadingMoreMessages,
    loadMoreMessages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    append,
    conversation: conversationData,
    isLoadingConversation,
  } = conversation

  // Compute context count from tab store or conversation metadata
  let _meta: any = (conversationData as any)?.metadata
  if (typeof _meta === 'string') {
    try {
      _meta = JSON.parse(_meta)
    } catch {
      _meta = undefined
    }
  }
  const contextCount =
    tab.aiPaneData?.contextIds?.length ?? _meta?.contextIds?.length ?? 0

  // Auto-fix: If tab has no contextIds but conversation metadata has them, update the tab
  useEffect(() => {
    if (
      conversationData &&
      _meta?.contextIds?.length > 0 &&
      (!tab.aiPaneData?.contextIds || tab.aiPaneData.contextIds.length === 0)
    ) {
      console.log(
        '[ChatTabContent] Auto-fixing missing contextIds in tab from conversation metadata'
      )
      updateTabAiPaneData(tab.id, {
        contextIds: _meta.contextIds,
      })
    }
  }, [
    conversationData,
    _meta?.contextIds,
    tab.aiPaneData?.contextIds,
    tab.id,
    updateTabAiPaneData,
  ])

  // Debug: log messages to inspect roles/content
  useEffect(() => {
    if (messages.length > 0) {
      // eslint-disable-next-line no-console
      console.log('[ChatTabContent] Render messages:', messages)
    }
  }, [messages])

  // ------------------------------------------------------------------
  // 🔗  Asset integration – turn every chat conversation into an Asset so
  //      the user can revisit past chats from the Asset sidebar.
  // ------------------------------------------------------------------

  const { addAsset, updateAsset, assets } = useAssetStore()

  // Debug context count calculation
  useEffect(() => {
    // Also check if there's an asset for this conversation
    const asset = assets.find(a => a.id === conversationId)

    console.log('[ChatTabContent] Context count debug:', {
      conversationId,
      'tab.aiPaneData?.contextIds': tab.aiPaneData?.contextIds,
      'tab.aiPaneData?.contextIds.length': tab.aiPaneData?.contextIds?.length,
      'tab.aiPaneData (full)': tab.aiPaneData,
      conversationData: conversationData,
      'conversationData.metadata (raw)': (conversationData as any)?.metadata,
      'typeof metadata': typeof (conversationData as any)?.metadata,
      '_meta (parsed)': _meta,
      '_meta?.contextIds': _meta?.contextIds,
      '_meta?.contextIds.length': _meta?.contextIds?.length,
      'asset found': !!asset,
      'asset.contextIds': asset?.contextIds,
      'asset.contextIds.length': asset?.contextIds?.length,
      contextCount: contextCount,
    })
  }, [
    conversationId,
    tab.aiPaneData?.contextIds,
    conversationData,
    _meta,
    contextCount,
    assets,
  ])

  // Helper: generate title from first user message if no explicit title
  const generateChatAssetTitle = (firstUserMsg: string): string => {
    const words = firstUserMsg.trim().split(' ').slice(0, 6)
    let title = words.join(' ')
    if (title.length > 50) title = title.slice(0, 47) + '…'
    return title || 'Chat'
  }

  // Create the asset once the first assistant response has completed.
  const assetIdRef = useRef<string | null>(tab.aiPaneData?.assetId || null)

  // Timeout tracking for cleanup
  const submitTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Create asset when we have conversation ID and at least one assistant response
    // Use tab.aiPaneData?.conversationId instead of conversationData?.id to avoid race condition
    const conversationId =
      tab.aiPaneData?.conversationId || conversationData?.id
    if (!conversationId) {
      console.log(
        '💾 [ChatTab] No conversation ID available for asset creation'
      )
      return
    }

    // Check if asset already created (tab or global store)
    if (assetIdRef.current) {
      console.log('💾 [ChatTab] Asset already created:', assetIdRef.current)
      return
    }

    const lastMsg = messages[messages.length - 1]
    if (!lastMsg || lastMsg.role !== 'assistant' || lastMsg.isStreaming) {
      console.log('💾 [ChatTab] Waiting for assistant response to complete')
      return
    }

    // Ensure there is at least one user message to derive prompt/title
    const firstUser = messages.find(m => m.role === 'user')
    if (!firstUser) {
      console.log('💾 [ChatTab] No user message found for asset creation')
      return
    }

    // Avoid duplicate creation across hot reloads
    if (assets.some(a => a.id === conversationId)) {
      console.log('💾 [ChatTab] Asset already exists in store:', conversationId)
      assetIdRef.current = conversationId
      return
    }

    console.log(
      '💾 [ChatTab] Creating chat asset for conversation:',
      conversationId
    )
    const chatTitle =
      conversationData?.title || generateChatAssetTitle(firstUser.content)

    const newAssetId = addAsset({
      id: conversationId,
      title: chatTitle,
      content: '', // Chat assets rely on messages array instead of single blob
      type: 'chat',
      model: tab.aiPaneData?.model || 'gpt-4.1',
      prompt: firstUser.content,
      contextIds: tab.aiPaneData?.contextIds || [],
      dragTreeId: dragTreeId,
      messages: messages
        .filter(m => m.role === 'user' || m.role === 'assistant')
        .map(m => ({
          role: m.role as 'user' | 'assistant',
          content: m.content,
        })),
      persistedInDb: true,
      isContentLoaded: true,
      createdAt: new Date(),
      viewed: false,
    })

    // Update the tab title so it matches the new asset title (mirrors Generate behaviour)
    updateTabTitle(tab.id, chatTitle)

    // Store assetId on the tab to prevent re-adding
    updateTabAiPaneData(tab.id, { assetId: newAssetId })
    assetIdRef.current = newAssetId
    console.log('💾 [ChatTab] Chat asset created successfully:', newAssetId)

    // Title will be auto-generated by the backend during first chat turn
  }, [
    tab.aiPaneData?.conversationId,
    conversationData?.id,
    conversationData?.title,
    messages,
    assets,
    addAsset,
    dragTreeId,
    tab.aiPaneData?.model,
    tab.aiPaneData?.contextIds,
    updateTabAiPaneData,
    tab.id,
    updateTabTitle,
    updateAsset,
  ])

  const prevLoadingRef = useRef<boolean>(false)

  useEffect(() => {
    const justFinishedStreaming = prevLoadingRef.current && !isLoading
    prevLoadingRef.current = isLoading

    if (!assetIdRef.current || !justFinishedStreaming) return

    updateAsset(assetIdRef.current, {
      messages: messages
        .filter(m => m.role === 'user' || m.role === 'assistant')
        .map(m => ({
          role: m.role as 'user' | 'assistant',
          content: m.content,
        })),
      updatedAt: new Date(),
    })
  }, [isLoading, messages, updateAsset])

  // Update editable title when tab title changes
  useEffect(() => {
    setEditableTitle(tab.title || '')
  }, [tab.title])

  // Cleanup timeouts on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (submitTimeoutRef.current) {
        clearTimeout(submitTimeoutRef.current)
        submitTimeoutRef.current = null
      }
    }
  }, [])

  // Handle title save
  const handleTitleSave = () => {
    const title = editableTitle.trim()
    if (title.length === 0) return
    updateTabTitle(tab.id, title)
    // Update corresponding asset title if exists
    if (assetIdRef.current) {
      updateAsset(assetIdRef.current, { title })
    }
    // Persist to server
    if (tab.aiPaneData?.conversationId) {
      fetch(
        `/api/aipane/conversations/${tab.aiPaneData.conversationId}/title`,
        {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ title }),
        }
      ).catch(err => console.error('Failed to update title:', err))
    }
    setIsEditingTitle(false)
  }

  // Get context content from selected drag tree nodes
  function getContextContent(): string {
    if (!tab.aiPaneData?.contextIds) return ''

    return tab.aiPaneData.contextIds
      .map(nodeId => {
        const contentMap = nodeContent.get(nodeId)
        if (!contentMap || contentMap.size === 0) return ''
        const firstContentItem = Array.from(contentMap.values())[0]
        let text = (firstContentItem as any)?.contentText || ''
        if (typeof text === 'object' && text.type === 'doc') {
          text = convertTiptapJsonToMarkdown(text)
        } else if (typeof text === 'string') {
          try {
            const parsed = JSON.parse(text)
            if (parsed && parsed.type === 'doc') {
              text = convertTiptapJsonToMarkdown(parsed)
            }
          } catch (_e) {}
        }
        return text as string
      })
      .filter(Boolean)
      .join('\n\n')
  }

  // Initialize chat with welcome message if needed
  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    if (
      tab.aiPaneData &&
      isConversationReady && // Only proceed when conversation is ready
      messages.length === 0 &&
      !isInitializing
    ) {
      setIsInitializing(true)

      if (tab.aiPaneData.assetContent) {
        // Load from asset - this is static content, not streaming
        append({
          role: 'assistant',
          content: tab.aiPaneData.assetContent,
        })
        // Mark as complete since this isn't streaming
        timeoutId = setTimeout(() => setIsInitializing(false), 100)
      } else if (
        tab.aiPaneData?.assetMessages &&
        tab.aiPaneData?.assetMessages.length > 0
      ) {
        // Prefill historical chat messages so the sidebar click feels instant.
        // We append them in original order before the live conversation hook
        // finishes fetching from the server. Duplicate filtering is handled
        // later when persisted messages arrive.
        tab.aiPaneData.assetMessages.forEach(m => append(m))
        timeoutId = setTimeout(() => setIsInitializing(false), 100)
      } else if (tab.aiPaneData.prompt) {
        // Start with initial prompt (e.g. quick-research)
        append({
          role: 'user',
          content: tab.aiPaneData.prompt,
        })

        // Immediately clear the stored prompt so that if the user reloads the
        // page we don’t automatically fire the same API call again.
        updateTabAiPaneData(tab.id, { prompt: '' })

        timeoutId = setTimeout(() => setIsInitializing(false), 100)
      } else {
        setIsInitializing(false)
      }
    }

    // Cleanup timeout on dependency change or unmount
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [
    tab.aiPaneData,
    isConversationReady,
    messages.length,
    append,
    isInitializing,
    updateTabAiPaneData,
    tab.id,
  ])

  // Process messages for display
  const cleanMessages = useMemo(() => {
    const filtered = messages
      .filter(m => (m.role || '').toLowerCase() !== 'system')
      // Ensure chronological order (oldest first)
      .sort(
        (a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      )

    return filtered.map((message, index) => {
      const roleLower = (message.role || '').toLowerCase()
      const isCurrentMessage = index === filtered.length - 1
      const isAssistantMessage = roleLower === 'assistant'
      const messageIsStreaming =
        isLoading && isCurrentMessage && isAssistantMessage

      return {
        ...message,
        cleanContent: message.content,
        stepCount: message.stepCount || 0,
        isStreaming: messageIsStreaming || message.isStreaming,
      }
    })
  }, [messages, isLoading])

  // Handle scroll events from InfiniteScrollContainer
  const handleScroll = useCallback(
    (info: {
      scrollTop: number
      scrollHeight: number
      clientHeight: number
      isAtTop: boolean
      isAtBottom: boolean
    }) => {
      setScrollInfo(info)
    },
    []
  )

  // Auto-scroll to bottom when new messages arrive
  const shouldAutoScroll = scrollInfo.isAtBottom || isLoading

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  // Copy message content to clipboard
  const copyMessage = useCallback(
    async (content: string, _messageId: string) => {
      try {
        // Content is already clean - no parsing needed
        if (navigator?.clipboard?.writeText) {
          await navigator.clipboard.writeText(content)
        } else {
          throw new Error('Clipboard API not available')
        }

        // Show success toast
        toast.success('Message copied to clipboard', {
          duration: 2000,
          position: 'bottom-right',
        })
      } catch (error) {
        console.error('Failed to copy message:', error)
        toast.error('Failed to copy message', {
          duration: 2000,
          position: 'bottom-right',
        })
      }
    },
    []
  )

  // Handle form submission with debouncing to prevent multiple submissions
  const onSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()
      if (isLoading || isSubmitting || !input.trim()) return

      setIsSubmitting(true)
      handleSubmit(e)

      // Clear any existing timeout
      if (submitTimeoutRef.current) {
        clearTimeout(submitTimeoutRef.current)
      }

      // Reset submitting state after a delay
      submitTimeoutRef.current = setTimeout(() => setIsSubmitting(false), 500)
    },
    [handleSubmit, isLoading, isSubmitting, input]
  )

  // Handle Enter key in textarea with debouncing
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (
        e.key === 'Enter' &&
        !e.shiftKey &&
        !isLoading &&
        !isSubmitting &&
        input.trim()
      ) {
        e.preventDefault()
        onSubmit(e as any)
      }
    },
    [onSubmit, isLoading, isSubmitting, input]
  )

  // Format timestamp
  const formatTime = (date: Date): string => {
    // Defensively handle invalid dates
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return ''
    }
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // TruncatedContentProps type definition
  type TruncatedContentProps = {
    content: string
    maxLines?: number
  }

  const TruncatedContentComponent: React.FC<TruncatedContentProps> = ({
    content,
    maxLines = 8,
  }) => {
    const [isExpanded, setIsExpanded] = useState(false)
    const lines = content.split('\n')
    const shouldTruncate = lines.length > maxLines

    if (!shouldTruncate || isExpanded) {
      return <span className="whitespace-pre-wrap">{content}</span>
    }

    const truncatedContent = lines.slice(0, maxLines).join('\n')

    return (
      <div>
        <span className="whitespace-pre-wrap">{truncatedContent}</span>
        <button
          onClick={() => setIsExpanded(true)}
          className="text-blue-500 hover:text-blue-700 text-sm mt-2 block"
        >
          ... See more ({lines.length - maxLines} more lines)
        </button>
      </div>
    )
  }

  // Removed unused TruncatedContent component

  return (
    <div className="h-full min-h-0 flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <FiMessageCircle className="w-5 h-5 text-green-600" />
          </div>
          <div>
            {isEditingTitle ? (
              <Input
                value={editableTitle}
                onChange={e => setEditableTitle(e.target.value)}
                onBlur={handleTitleSave}
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleTitleSave()
                  }
                }}
                className="h-7 text-sm px-2 py-1"
                autoFocus
              />
            ) : (
              <div className="flex items-center space-x-1 group">
                <h2 className="text-lg font-semibold text-gray-900">
                  {editableTitle || conversationData?.title || 'AI Chat'}
                </h2>
                <button
                  onClick={() => setIsEditingTitle(true)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-500 hover:text-gray-700"
                  title="Edit title"
                >
                  <FiEdit2 className="w-4 h-4" />
                </button>
              </div>
            )}
            {/* Optional subheading removed per UX request */}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowContextDialog(true)}
            className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100"
            title="Context items"
          >
            <span>Using {contextCount} context</span>
          </button>
          <button
            onClick={() => setShowSettingsModal(true)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded hover:bg-gray-100"
            title="Chat settings"
          >
            <FiSettings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Chat Interface - Feature Flag Conditional */}
      {featureFlags.assistantUI ? (
        /* New Assistant-UI Implementation */
        <div className="flex-1 min-h-0">
          {conversationError ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center gap-3 text-red-500 max-w-md text-center">
                <FiMessageCircle className="w-8 h-8" />
                <span className="text-sm font-medium">
                  Failed to initialize chat
                </span>
                <span className="text-xs text-gray-500">
                  {conversationError}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setConversationError(null)
                    window.location.reload()
                  }}
                >
                  Retry
                </Button>
              </div>
            </div>
          ) : !isConversationReady ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center gap-3 text-gray-500">
                <FiMessageCircle className="w-8 h-8 animate-pulse" />
                <span className="text-sm">Initializing chat...</span>
                {conversationError && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setConversationError(null)
                      // Clear error to allow retry
                    }}
                  >
                    Retry
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <>
              {isLoadingConversation ? (
                <div className="flex items-center justify-center h-full">
                  <div className="flex flex-col items-center gap-2 text-gray-500 animate-pulse">
                    <FiMessageCircle className="w-8 h-8" />
                    <span className="text-sm">Loading conversation...</span>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col h-full min-h-0">
                  {/* Simple Message List */}
                  <SimpleMessageList
                    messages={messages}
                    isLoading={isLoading}
                    className="flex-1 min-h-0"
                  />

                  {/* Simple Chat Input */}
                  <SimpleChatInput
                    input={input}
                    onInputChange={handleInputChange}
                    onSubmit={onSubmit}
                    disabled={!isConversationReady || isSubmitting}
                    isLoading={isLoading}
                    placeholder={
                      !isConversationReady
                        ? 'Setting up chat...'
                        : 'Message AI Assistant...'
                    }
                  />
                </div>
              )}
            </>
          )}
        </div>
      ) : (
        /* Legacy Implementation */
        <>
          {/* Messages Area with Infinite Scroll */}
          <div className="flex-1 min-w-0 min-h-0 relative">
            {/* Error state */}
            {conversationError && (
              <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center gap-3 text-red-500 max-w-md text-center">
                  <FiMessageCircle className="w-8 h-8" />
                  <span className="text-sm font-medium">
                    Failed to initialize chat
                  </span>
                  <span className="text-xs text-gray-500">
                    {conversationError}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setConversationError(null)
                      window.location.reload()
                    }}
                  >
                    Retry
                  </Button>
                </div>
              </div>
            )}

            {/* Message list (always rendered) */}
            {!conversationError && (
              <InfiniteScrollContainer
                hasMore={hasMoreMessages}
                isLoading={isLoadingMoreMessages}
                onLoadMore={loadMoreMessages}
                autoScrollToBottom={shouldAutoScroll}
                onScroll={handleScroll}
                className="h-full"
              >
                <div className="p-4 space-y-4">
                  {cleanMessages.map(m => (
                    <pre
                      key={m.id}
                      className="whitespace-pre-wrap p-2 border-b"
                    >
                      [{m.role}] {m.content}
                    </pre>
                  ))}

                  {/* Loading indicator - only show for actual streaming, not initialization */}
                  {isLoading && !isInitializing && (
                    <div className="flex justify-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <FiCpu className="w-4 h-4 text-blue-600" />
                        </div>
                      </div>
                      <div className="bg-gray-100 rounded-lg p-3">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                          <div
                            className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                            style={{ animationDelay: '0.1s' }}
                          />
                          <div
                            className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                            style={{ animationDelay: '0.2s' }}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </InfiniteScrollContainer>
            )}

            {/* Overlay spinner while initial conversation is loading */}
            {!conversationError && isLoadingConversation && (
              <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
                <div className="flex flex-col items-center gap-2 text-gray-500 animate-pulse">
                  <FiMessageCircle className="w-8 h-8" />
                  <span className="text-sm">Loading conversation...</span>
                </div>
              </div>
            )}

            {/* Scroll to bottom button */}
            {!scrollInfo.isAtBottom && (
              <button
                onClick={scrollToBottom}
                className="absolute bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-colors duration-200 z-10"
                aria-label="Scroll to bottom"
              >
                <FiArrowDown className="w-5 h-5" />
              </button>
            )}
          </div>

          {/* Input Area - ChatGPT Style */}
          <div className="border-t border-gray-200 bg-white">
            <div className="max-w-2xl mx-auto px-4 py-4">
              <form onSubmit={onSubmit} className="relative">
                <div className="relative flex items-end bg-gray-50 rounded-xl border border-gray-200 focus-within:border-blue-500 transition-colors">
                  <Textarea
                    ref={textareaRef}
                    value={input}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    placeholder={
                      !isConversationReady
                        ? 'Setting up chat...'
                        : 'Message AI Assistant...'
                    }
                    className="flex-1 resize-none max-h-32 min-h-[44px] bg-transparent border-none shadow-none focus:ring-0 focus:outline-none text-gray-900 placeholder-gray-500 px-4 py-3 text-sm leading-relaxed text-left"
                    rows={1}
                    disabled={!isConversationReady || isLoading || isSubmitting}
                  />
                  <Button
                    type="submit"
                    disabled={
                      !isConversationReady ||
                      !input.trim() ||
                      isLoading ||
                      isSubmitting
                    }
                    size="sm"
                    className={cn(
                      'ml-2 mr-3 mb-3 w-8 h-8 p-0 rounded-lg transition-all duration-200 flex items-center justify-center',
                      isConversationReady &&
                        input.trim() &&
                        !isLoading &&
                        !isSubmitting
                        ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm hover:shadow-md'
                        : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    )}
                  >
                    <FiSend className="w-4 h-4" />
                  </Button>
                </div>
              </form>

              {/* Help text */}
              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <div className="flex items-center space-x-2">
                  <span>Press Enter to send, Shift+Enter for new line</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>
                    {useRealLLMAPI ? 'Azure OpenAI' : 'Simulator'} •
                    {tab.aiPaneData?.model || 'gpt-4.1'} •
                    {tab.aiPaneData?.contextIds.length || 0} context items
                  </span>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Context Items Dialog */}
      <Dialog open={showContextDialog} onOpenChange={setShowContextDialog}>
        <DialogContent className="max-w-2xl max-h-[70vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              Context Items ({tab.aiPaneData?.contextIds.length || 0})
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto space-y-2 max-h-[60vh]">
            {tab.aiPaneData?.contextIds?.map((nodeId, index) => {
              const node = findNodeById(nodeId)
              const nodeLabel = node?.label || `Item ${index + 1}`

              const contentMap = nodeContent.get(nodeId)
              let previewText = 'No content available'

              if (contentMap && contentMap.size > 0) {
                const firstContentItem = Array.from(contentMap.values())[0]
                let text = (firstContentItem as any)?.contentText || ''

                // Convert content to readable format
                if (typeof text === 'object' && text.type === 'doc') {
                  text = convertTiptapJsonToMarkdown(text)
                } else if (typeof text === 'string') {
                  try {
                    const parsed = JSON.parse(text)
                    if (parsed && parsed.type === 'doc') {
                      text = convertTiptapJsonToMarkdown(parsed)
                    }
                  } catch (_e) {}
                }
                previewText =
                  typeof text === 'string' ? text : 'Content available'
              }

              return (
                <button
                  key={nodeId}
                  onClick={() => navigateToContextItem(nodeId)}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200 group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded font-mono">
                          {index + 1}
                        </span>
                        <div className="text-sm font-medium text-gray-900 group-hover:text-blue-700 truncate">
                          {nodeLabel}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2 line-clamp-3">
                        {previewText.substring(0, 200)}
                        {previewText.length > 200 && '...'}
                      </div>
                    </div>
                    <div className="ml-2 text-xs text-gray-400 group-hover:text-blue-500 flex-shrink-0">
                      Click to navigate
                    </div>
                  </div>
                </button>
              )
            })}

            {(!tab.aiPaneData?.contextIds ||
              tab.aiPaneData.contextIds.length === 0) && (
              <div className="text-center text-gray-500 py-8">
                No context items selected
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Chat Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Model</span>
              <span className="text-gray-900">
                {tab.aiPaneData?.model || 'gpt-4.1'}
              </span>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Messages</span>
              <span className="text-gray-900">{messages.length}</span>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Conversation ID</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-900 font-mono text-xs">
                  {conversationId || 'Not initialized'}
                </span>
                {conversationId && (
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(conversationId)
                      toast.success('Conversation ID copied!')
                    }}
                    className="text-gray-500 hover:text-gray-700"
                    title="Copy conversation ID"
                  >
                    <FiCopy className="w-3 h-3" />
                  </button>
                )}
              </div>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Context Count</span>
              <span className="text-gray-900">{contextCount}</span>
            </div>

            {tab.aiPaneData?.contextIds &&
              tab.aiPaneData.contextIds.length > 0 && (
                <div className="text-sm flex items-center justify-between">
                  <span className="font-medium text-gray-700">
                    Context Items
                  </span>
                  <button
                    onClick={() => {
                      setShowContextDialog(true)
                      setShowSettingsModal(false)
                    }}
                    className="text-blue-600 underline hover:text-blue-800"
                  >
                    {tab.aiPaneData.contextIds.length} selected
                  </button>
                </div>
              )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Performance monitoring in development */}
      <ConversationPerformanceMonitor
        conversationId={conversationData?.id}
        isLoading={isLoadingConversation}
        messageCount={messages.length}
      />
    </div>
  )
}

export default React.memo(ChatTabContent)
