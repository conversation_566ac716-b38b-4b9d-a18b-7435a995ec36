import { useCallback, useEffect } from 'react'
import { useChat } from 'ai/react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import type { SupportedLanguageCode } from '@/app/(conv)/screening/constants/languages'
import { isDevelopmentOrCi } from '@/lib/environment'

type UseScreeningRephraseProps = {
  description: string
  selectedLanguage: SupportedLanguageCode
  onRephrasedUpdate: (rephrased: string[]) => void
  useSimulator?: boolean // New prop to enable simulator mode
}

type UseScreeningRephraseReturn = {
  isStreaming: boolean
  startRephrase: () => void
}

/**
 * Custom hook for handling real-time screening rephrase streaming
 * Manages the problem statement generation with text streaming
 * Supports both real API and simulator modes for testing
 */
export const useScreeningRephrase = ({
  description,
  selectedLanguage,
  onRephrasedUpdate,
  useSimulator = false,
}: UseScreeningRephraseProps): UseScreeningRephraseReturn => {
  const { data: session } = useSession()

  // Choose API endpoint based on simulator mode
  const apiEndpoint = useSimulator
    ? '/api/screening/rephrase-simulator'
    : '/api/screening/rephrase'

  // Helper function to extract numbered suggestions from rephrase response
  const extractNumberedSuggestions = useCallback((text: string): string[] => {
    const lines = text.split('\n')
    const numberedLines = lines.filter(line => /^\d+\./.test(line.trim()))
    return numberedLines.map(line => line.replace(/^\d+\.\s*/, '').trim())
  }, [])

  // Create completely stable hooks configuration to prevent AI SDK from re-initializing
  const { messages, append, isLoading } = useChat({
    api: apiEndpoint,
    experimental_throttle: 100, // Throttle updates to every 100ms to prevent render loops
  })

  // Process streaming messages in real-time
  useEffect(() => {
    // Only process if we have messages
    if (messages.length === 0) return

    const lastMessage = messages[messages.length - 1]

    if (lastMessage?.role === 'assistant' && lastMessage.content) {
      // Show partial content as it streams in
      const parsed = extractNumberedSuggestions(lastMessage.content)
      if (parsed.length > 0) {
        onRephrasedUpdate(parsed)
      } else if (lastMessage.content.trim()) {
        // Show partial content even if not fully formatted yet
        onRephrasedUpdate([lastMessage.content])
      }
    }
  }, [messages, extractNumberedSuggestions, onRephrasedUpdate]) // Depend on messages to catch streaming updates

  // Handle completion toast separately to avoid re-triggering on every content update
  useEffect(() => {
    if (!isLoading && messages.length > 1) {
      const lastMessage = messages[messages.length - 1]
      if (lastMessage?.role === 'assistant' && lastMessage.content) {
        if (isDevelopmentOrCi()) {
          const mode = useSimulator ? 'Simulator' : 'Real API'
          toast.success(`Problem statements generated! (${mode})`)
        }
      }
    }
  }, [isLoading, messages.length, useSimulator]) // Only trigger on completion

  const startRephrase = useCallback(() => {
    // In simulator mode, we don't need authentication
    if (!useSimulator && !session?.user?.id) {
      toast.error('Please log in to use the real API.')
      return
    }

    // Send the required data along with a dummy message to start the stream
    append(
      {
        role: 'system',
        content: 'start rephrasing questions',
      },
      {
        body: {
          userId: session?.user?.id ?? '',
          description,
          preferredLanguage: selectedLanguage,
        },
      }
    )
  }, [session?.user?.id, append, useSimulator, description, selectedLanguage])

  return {
    isStreaming: isLoading,
    startRephrase,
  }
}
