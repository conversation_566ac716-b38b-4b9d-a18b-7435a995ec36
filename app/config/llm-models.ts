import { SubscriptionTier } from '@prisma/client'

/**
 * Supported LLM model providers
 */
export type ModelProvider = 'azure' | 'openai' | 'anthropic' | 'google'

/**
 * Configuration for a specific LLM model
 */
export type ModelConfig = {
  model: string
  model_provider: ModelProvider
  temperature?: number
  maxTokens?: number
  rateLimitWindowMs?: number // cooldown window per user per route
  rateLimitMaxRequests?: number // max allowed within window
  maxPromptChars?: number // hard cap on input size (characters)
  // Additional provider-specific settings can be added here
}

/**
 * API routes that use LLM models
 * Add new routes here as they are implemented
 */
export type APIRoute =
  | 'dragtree_generate_questions'
  | 'dragtree_research_generate'
  | 'dragtree_generate_similar_questions'
  | 'dragtree_generate_similar_categories'
  | 'screening_diagnose'
  | 'screening_rephrase'
  | 'aipane_generate'
  | 'aipane_chat'

/**
 * Model configuration for all API routes for a specific subscription tier
 */
export type APIRouteConfig = {
  [K in APIRoute]: ModelConfig
}

/**
 * Complete model configuration mapping subscription tiers to API route configs
 */
export type SubscriptionModelConfig = {
  [K in SubscriptionTier]: APIRouteConfig
}

/**
 * Centralized LLM model configuration
 *
 * Strategy:
 * - FREE tier: Cost-effective models for basic functionality
 * - GUEST tier: Premium models for enhanced experience
 *
 * Future tiers can be easily added by extending the SubscriptionTier enum
 * and adding configurations here.
 */
export const LLM_MODEL_CONFIG: SubscriptionModelConfig = {
  FREE: {
    dragtree_generate_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
      rateLimitWindowMs: 1000, // cooldown window per user per route
      rateLimitMaxRequests: 5, // max allowed within window
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
    },
    screening_diagnose: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
    },
    screening_rephrase: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
    },
    aipane_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
    },
    aipane_chat: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 10000,
    },
  },
  GUEST: {
    dragtree_generate_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
    },
    dragtree_research_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
      rateLimitWindowMs: 1000, // cooldown window per user per route
      rateLimitMaxRequests: 5, // max allowed within window
    },
    dragtree_generate_similar_questions: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
    },
    dragtree_generate_similar_categories: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
    },
    screening_diagnose: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
    },
    screening_rephrase: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
    },
    aipane_generate: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
    },
    aipane_chat: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 30000,
    },
  },
}

/**
 * Default fallback configuration if user tier is not found or invalid
 */
export const DEFAULT_MODEL_CONFIG: ModelConfig = {
  model: 'gpt-4.1',
  model_provider: 'azure',
  temperature: 0.7,
  maxTokens: 10000,
}

/**
 * Validates that all subscription tiers have configurations for all API routes
 * This function helps catch configuration errors at build time
 */
export function validateModelConfig(): boolean {
  const requiredTiers: SubscriptionTier[] = ['FREE', 'GUEST']
  const requiredRoutes: APIRoute[] = [
    'dragtree_generate_questions',
    'dragtree_research_generate',
    'dragtree_generate_similar_questions',
    'dragtree_generate_similar_categories',
    'screening_diagnose',
    'screening_rephrase',
    'aipane_generate',
    'aipane_chat',
  ]

  for (const tier of requiredTiers) {
    if (!LLM_MODEL_CONFIG[tier]) {
      console.error(`Missing configuration for subscription tier: ${tier}`)
      return false
    }

    for (const route of requiredRoutes) {
      if (!LLM_MODEL_CONFIG[tier][route]) {
        console.error(`Missing configuration for tier ${tier}, route: ${route}`)
        return false
      }
    }
  }

  return true
}

// Validate configuration on module load
if (!validateModelConfig()) {
  throw new Error('Invalid LLM model configuration detected')
}
