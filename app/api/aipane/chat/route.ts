import { streamText, createDataStreamResponse } from 'ai'
import { azure } from '@ai-sdk/azure'
import { AIPaneChatRequest } from './types'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  persistConversationTurn,
  createExecutionStepCollector,
  addMessageToConversation,
} from '@/app/server-actions/ai-chat'
import {
  buildSearchTools,
  type SearchProgressCallback,
} from '@/app/api/dragtree/shared/search-tools'
import { type SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'
import { standardErrors } from '../shared/errors'
import { buildContextMessages } from '@/app/libs/llmContext'
import { Ai<PERSON>hatLogger } from '@/app/server-actions/ai-chat/logging'
import { getModelConfig } from '@/app/libs/model-config'
import {
  getLLMLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'
import prisma from '@/app/libs/prismadb'

// Import configuration constants
import {
  RATE_LIMIT_CONFIG,
  MODEL_CONFIG,
  RETRY_CONFIG,
  TOOL_NAMES,
} from './chat-config'
// Note: persistTurn helper removed due to API incompatibility

export const maxDuration = 60

// Initialize structured logger
const chatLogger = new AiChatLogger('aipane-chat-route')

export async function POST(req: Request) {
  try {
    // Retrieve the authenticated user session to obtain userId for logging
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return standardErrors.unauthorized()
    }
    const userId = session.user.id

    // Rate limiting - using config constants
    const rateLimitKey = `${userId}:aipane:chat`
    if (isRateLimited(rateLimitKey, RATE_LIMIT_CONFIG.WINDOW_MS)) {
      const retryAfter = getRetryAfterSeconds(
        rateLimitKey,
        RATE_LIMIT_CONFIG.WINDOW_MS
      )
      return standardErrors.rateLimited(
        retryAfter,
        RATE_LIMIT_CONFIG.MAX_REQUESTS.toString()
      )
    }

    // Parse request body with proper typing
    let requestData: AIPaneChatRequest
    try {
      const rawData = await req.json()
      requestData = rawData as AIPaneChatRequest
    } catch (error) {
      console.error('🤖 [Chat Real] Failed to parse JSON:', error)
      return standardErrors.invalidJson()
    }

    if (!requestData) {
      return standardErrors.missingBody()
    }

    chatLogger.info('Received chat request', {
      userId,
    })

    // Extract and validate request data
    const rawMessages = requestData.messages

    // Get model configuration based on user's subscription tier
    const modelConfig = await getModelConfig(userId, 'aipane_chat')
    const model = modelConfig.model

    const context = requestData.context
    const settings = requestData.settings || {}
    let conversationId = requestData.conversationId
    const contextEntityType = requestData.contextEntityType
    const contextEntityId = requestData.contextEntityId
    const contextIds = requestData.contextIds || []

    // Validate raw messages
    if (!rawMessages || !Array.isArray(rawMessages)) {
      return standardErrors.invalidMessages()
    }

    // Use the extracted content normalization utility
    // The Vercel AI SDK v2 sends message.content as an *array* of parts to
    // support future multimodal messages. We normalize to string format.
    const { normaliseMessageContent } = await import(
      '@/app/libs/ai-chat/content'
    )

    const messages = rawMessages.map(m => ({
      ...m,
      content: normaliseMessageContent((m as any).content),
    }))

    // ConversationId is mandatory now to avoid duplicate rows
    if (!conversationId || !conversationId.startsWith('thread_')) {
      return standardErrors.invalidIdFormat('conversationId', 'thread_*')
    }

    // Upsert conversation row if it doesn't exist yet (idempotent)
    await prisma.aiConversation.upsert({
      where: { id: conversationId },
      create: {
        id: conversationId,
        userId: session.user.id,
        title: 'Untitled chat', // Will be auto-generated after first turn
        contextEntityType: contextEntityType || 'drag_tree',
        contextEntityId: contextEntityId || '',
        metadata: { contextIds: contextIds || [] }, // Preserve context IDs
      },
      update: {}, // Nothing to update on repeat calls
    })

    // Validate messages
    if (!Array.isArray(messages) || messages.length === 0) {
      return standardErrors.invalidMessages(
        'Messages array must contain at least one message'
      )
    }

    chatLogger.info('Processing chat request', {
      userId,
    })

    // Fetch language preference if context is a drag tree
    let preferredLanguage: SupportedLanguageCode = 'en'
    if (contextEntityType === 'drag_tree' && contextEntityId) {
      try {
        const dragTree = await prisma.dragTree.findUnique({
          where: { id: contextEntityId },
          select: { preferred_language: true },
        })
        if (dragTree?.preferred_language) {
          preferredLanguage =
            dragTree.preferred_language as SupportedLanguageCode
        }
      } catch (error) {
        console.warn('Failed to fetch drag tree language preference:', error)
      }
    }

    const languageName = getLLMLanguageName(preferredLanguage)

    // (auto-creation removed – must be created via /conversations)

    // Prepare messages for AI model
    const latestUser = messages[messages.length - 1]
    // Build the base message list (system prompt + history + latest user msg)
    const { messages: baseMessages } = await buildContextMessages(
      conversationId,
      {
        role: 'user',
        content: latestUser.content,
      },
      100_000
    )

    // ──────────────────────────────────────────────────────────
    // Combine system prompt + context into ONE message to reduce tokens.
    // 1. Start with default system prompt (always baseMessages[0]).
    // 2. Determine context text – from current request or from the first
    //    persisted system message that starts with "CONTEXT:".
    // 3. Build a merged prompt:
    //    "You are …\n\nWe did some quick researches, below are the findings:\n<ctx>"
    // 4. Remove the standalone CONTEXT system message (if any).
    // ──────────────────────────────────────────────────────────

    const defaultPrompt = baseMessages[0]

    // Try to get context from request first (brand-new convo)
    let ctxText: string | undefined =
      context && context.trim().length > 0 ? context : undefined

    // Otherwise look for a persisted CONTEXT message
    if (!ctxText && baseMessages.length > 1) {
      const maybeCtx = baseMessages[1]
      if (
        maybeCtx.role === 'system' &&
        maybeCtx.content.startsWith('CONTEXT:\n')
      ) {
        ctxText = maybeCtx.content.replace(/^CONTEXT:\n/, '')
      }
    }

    let mergedSystemContent = defaultPrompt.content
    if (ctxText) {
      mergedSystemContent += `\n\nWe did some quick researches, below are the findings:\n${ctxText}`
    }

    // Add language instruction to system prompt
    mergedSystemContent += `\n\nPlease respond in ${languageName}.`

    // Build final message array: merged system + rest without duplicate ctx msg
    const modelMessages = [
      {
        role: 'system' as const,
        content: mergedSystemContent,
      },
      // Skip the old context system msg if we merged it (index 1)
      ...baseMessages.slice(ctxText ? 2 : 1),
    ]

    // Initialize execution step collector and search metadata
    const stepCollector = createExecutionStepCollector()
    const searchMetadata: SearchMetadata[] = []
    let finalResponse = ''

    // Real-time step streaming implementation

    // Create search progress callback for execution step tracking
    const searchProgressCallback: SearchProgressCallback = status => {
      chatLogger.debug('Search progress update', {
        userId,
      })

      // Add search execution steps to collector for persistence
      if (status.type === 'searching') {
        stepCollector.addToolCall(TOOL_NAMES.WEB_SEARCH, {
          query: status.query,
        })
      } else if (status.type === 'completed') {
        stepCollector.addToolResult(TOOL_NAMES.WEB_SEARCH, {
          query: status.query,
          resultCount: status.resultCount,
        })
      } else if (status.type === 'error') {
        stepCollector.addToolResult(TOOL_NAMES.WEB_SEARCH, {
          error: status.error,
        })
      }
    }

    // Use createDataStreamResponse for real-time step streaming
    return createDataStreamResponse({
      execute: dataStream => {
        // Initialize streaming
        dataStream.writeData({
          type: 'stream-start',
          conversationId,
          timestamp: Date.now(),
        })

        // Use Azure OpenAI for real chat with search tools and atomic persistence
        const result = streamText({
          model: azure(model),
          messages: modelMessages,
          tools: buildSearchTools(searchMetadata, searchProgressCallback),
          maxSteps: MODEL_CONFIG.MAX_STEPS,
          temperature: modelConfig.temperature,
          maxTokens: modelConfig.maxTokens,
          // Stream tool calls in real-time
          onChunk: async chunk => {
            // Capture tool calls and add them to the execution step collector
            if (chunk.chunk.type === 'tool-call') {
              stepCollector.addToolCall(chunk.chunk.toolName, chunk.chunk.args)

              // Stream the step data to the client
              dataStream.writeData({
                type: 'execution-step',
                step: {
                  type: 'TOOL_CALL',
                  toolName: chunk.chunk.toolName,
                  args: chunk.chunk.args,
                  toolCallId: chunk.chunk.toolCallId,
                  timestamp: Date.now(),
                },
              })
            }

            if (chunk.chunk.type === 'tool-result') {
              stepCollector.addToolResult(
                chunk.chunk.toolName,
                chunk.chunk.result
              )

              // Stream the step result to the client (optional - we're filtering these out in UI)
              dataStream.writeData({
                type: 'execution-step',
                step: {
                  type: 'TOOL_RESULT',
                  toolName: chunk.chunk.toolName,
                  result: chunk.chunk.result,
                  toolCallId: chunk.chunk.toolCallId,
                  timestamp: Date.now(),
                },
              })
            }
          },
          onFinish: async result => {
            // CRITICAL: This is where we do atomic persistence
            try {
              chatLogger.info('Stream finished, starting atomic persistence', {
                userId,
              })

              // Notify client that streaming is complete
              dataStream.writeData({
                type: 'stream-finish',
                timestamp: Date.now(),
              })

              // Get the latest user message
              const userMessages = messages.filter(m => m.role === 'user')
              const latestUserMessage =
                userMessages[userMessages.length - 1]?.content || ''

              // Use the clean content directly from the result
              finalResponse = result.text

              // Add reasoning summary if there were any tool calls or steps
              if (stepCollector.getStepCount() > 0) {
                const summary = `Generated response using ${searchMetadata.length} web search(es) and ${stepCollector.getStepCount()} execution step(s)`
                stepCollector.addReasoningSummary(summary)

                // Note: Reasoning summary step added to collector for persistence
              }

              // Log execution summary
              chatLogger.info('Execution summary', {
                userId,
              })

              // Persist the complete conversation turn atomically with basic retry logic
              const MAX_RETRIES = RETRY_CONFIG.MAX_RETRIES
              let attempt = 0
              let persistResult: Awaited<
                ReturnType<typeof persistConversationTurn>
              > | null = null

              const isFirstTurn = messages.length === 1 // only the new user msg

              // Persist context once at the start of the conversation so that
              // future calls automatically receive it via buildContextMessages.
              if (isFirstTurn && context && context.trim().length > 0) {
                try {
                  await addMessageToConversation(conversationId!, {
                    role: 'SYSTEM',
                    content: `CONTEXT:\n${context}`,
                  })
                  chatLogger.info('Persisted context message', { userId })
                } catch (_ctxErr) {
                  chatLogger.warn('Failed to persist context message', {
                    userId,
                  })
                }
              }

              while (attempt < MAX_RETRIES) {
                attempt++
                // eslint-disable-next-line no-await-in-loop
                persistResult = await persistConversationTurn({
                  conversationId: conversationId!,
                  userMessage: {
                    role: 'USER',
                    content: latestUserMessage,
                  },
                  assistantMessage: {
                    role: 'ASSISTANT',
                    content: finalResponse,
                    steps: stepCollector.getSteps(),
                  },
                })

                if (persistResult.success) break

                chatLogger.warn('Persistence attempt failed', {
                  userId,
                })
                // Back-off delay using config constant
                // eslint-disable-next-line no-await-in-loop
                await new Promise(res =>
                  setTimeout(res, RETRY_CONFIG.BACKOFF_MS * attempt)
                )
              }

              if (!persistResult?.success) {
                // CRITICAL: Log this failure – user saw response but it wasn't saved
                chatLogger.error(
                  'FATAL: Failed to save conversation turn after all retries',
                  {
                    userId,
                  }
                )
              } else {
                chatLogger.info('Successfully persisted conversation turn', {
                  userId,
                })
              }

              // Log AI usage for monitoring (keep existing logic)
              try {
                await createAIUsage({
                  userId: userId,
                  entityType: 'aipane',
                  entityId: conversationId!,
                  aiProvider: 'azure_openai',
                  modelName: model,
                  usageType: AIUsageType.CHAT,
                  inputPrompt: latestUserMessage,
                  messages: [
                    ...modelMessages,
                    { role: 'assistant', content: finalResponse },
                  ],
                  metadata: {
                    endpoint: '/api/aipane/chat',
                    promptTokens: result.usage?.promptTokens || 0,
                    completionTokens: result.usage?.completionTokens || 0,
                    totalTokens: result.usage?.totalTokens || 0,
                    finishReason: result.finishReason,
                    hasContext: !!context,
                    messageCount: messages.length,
                    conversationId,
                    executionSteps: stepCollector.getStepCount(),
                    hasThinking:
                      stepCollector.getSummary().stepsByType.THOUGHT > 0,
                    hasToolCalls:
                      stepCollector.getSummary().stepsByType.TOOL_CALL > 0,
                    searchResults: searchMetadata.length,
                  },
                  config: settings,
                })
              } catch (error) {
                // 1. preserve original Error, pass context separately
                chatLogger.error('Failed to log AI usage', error, {
                  userId,
                })
              }

              // Auto-generate title for new conversations (fire-and-forget)
              queueMicrotask(async () => {
                try {
                  const conversation = await prisma.aiConversation.findUnique({
                    where: { id: conversationId },
                    select: { title: true },
                  })

                  if (conversation?.title === 'Untitled chat') {
                    const { generateTitleWithWeakModel } = await import(
                      '@/app/libs/title-generation'
                    )
                    const title =
                      await generateTitleWithWeakModel(latestUserMessage)

                    await prisma.aiConversation.update({
                      where: { id: conversationId },
                      data: { title },
                    })

                    // Push title to client for live UI update
                    dataStream.writeData({ type: 'title', title })
                  }
                } catch (error) {
                  // Don't fail the main request if title generation fails
                  chatLogger.error(
                    'Failed to generate conversation title',
                    error,
                    { userId }
                  )
                }
              })
            } catch (error) {
              // CRITICAL: Log this failure - the user saw a response but it wasn't saved
              // 2. include the real Error object
              chatLogger.error(
                'FATAL: Failed to process conversation turn',
                error,
                { userId }
              )
            }
          },
        })

        // Merge the streamText result into the data stream
        result.mergeIntoDataStream(dataStream)
      },
      onError: error => {
        // 3. correct parameter order: (message, error, context)
        chatLogger.error('Stream error occurred', error, { userId })
        return error instanceof Error ? error.message : String(error)
      },
    })
  } catch (error) {
    // 4. outer-most catch: surface the actual error for easier debugging
    chatLogger.error('Chat API error', error)
    return standardErrors.internalError('Error generating response')
  }
}
