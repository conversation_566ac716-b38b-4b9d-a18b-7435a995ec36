import { NextRequest, NextResponse } from 'next/server'
import {
  Drag<PERSON><PERSON>Status,
  DragTreeNodeStatus,
  DragTree,
  AIUsageType,
} from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { updateDragTree } from '@/app/server-actions/drag-tree'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { exampleMarkdown } from '@/app/(conv)/dragTree/[dragTreeId]/utils/example'
import markdownToTreeNode from '@/app/(conv)/dragTree/[dragTreeId]/utils/markdownToTreeNode'
import { generateDragTreeNodeId } from '@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation'
import { streamText } from 'ai'
import { azure } from '@ai-sdk/azure'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'
import { getModelConfig } from '@/app/libs/model-config'
import {
  validateDragTreeOperation,
  validateRequestBody,
} from '@/app/libs/validation-utils'
import { generateQuestionsSchema } from '@/app/libs/api-schemas'

export const maxDuration = 60

// Types for better organization
type DragTreeMetadata = {
  useStaticData?: boolean
  originalUserPrompt?: string
  problemAnalysisItems?: {
    identified_issue?: string[]
    identified_intention?: string[]
    identified_entities?: string[]
  }
}

type ProcessingResult = {
  success: boolean
  error?: string
}

/**
 * Main API handler for generating drag tree questions
 */
export async function POST(request: NextRequest) {
  let dragTreeId: string | undefined

  try {
    console.log('🤖 [Drag Tree Generate] Starting streaming generation...')

    // Validate request body
    const bodyResult = await validateRequestBody(
      request,
      generateQuestionsSchema
    )
    if (!bodyResult.success) {
      return bodyResult.error
    }

    dragTreeId = bodyResult.data.dragTreeId

    // Validate authentication, ownership, and status in one call
    const validationResult = await validateDragTreeOperation(
      dragTreeId,
      DragTreeStatus.INITIALIZED
    )
    if (!validationResult.success) {
      return validationResult.error
    }

    const { userId, dragTree } = validationResult

    // Update status to prevent double triggers
    const statusResult = await updateTreeStatusToGenerating(dragTree.id)
    if (!statusResult.success) {
      return NextResponse.json(
        { error: statusResult.error || 'Failed to start generation process' },
        { status: 500 }
      )
    }

    // Handle static data for testing
    if (shouldUseStaticData(dragTree)) {
      return await handleStaticDataGeneration(dragTree.id)
    }

    // Generate content using AI streaming
    return await generateContentWithAI(dragTree, userId)
  } catch (error) {
    console.error('💥 [Drag Tree Generate] Unexpected error:', error)
    return handleUnexpectedError(dragTreeId)
  }
}

/**
 * Update tree status to GENERATING to prevent double triggers
 */
async function updateTreeStatusToGenerating(
  treeId: string
): Promise<ProcessingResult> {
  const updateResult = await updateDragTree({
    treeId: treeId,
    status: DragTreeStatus.GENERATING,
  })

  if (!updateResult.success) {
    console.error(
      '❌ [Drag Tree Generate] Failed to update status to GENERATING:',
      updateResult.error
    )
    return { success: false, error: 'Failed to start generation process' }
  }

  console.log('⏳ [Drag Tree Generate] Updated status to GENERATING')
  return { success: true }
}

/**
 * Check if static data should be used for testing
 */
function shouldUseStaticData(dragTree: any): boolean {
  const metadata = (dragTree.metadata as DragTreeMetadata) || {}
  return metadata.useStaticData || false
}

/**
 * Handle static data generation for testing
 */
async function handleStaticDataGeneration(
  treeId: string
): Promise<NextResponse> {
  console.log('📊 [Drag Tree Generate] Using static data for testing')

  try {
    await processGeneratedMarkdown(treeId, exampleMarkdown)
    return NextResponse.json({
      success: true,
      message: 'Static content generation completed successfully',
    })
  } catch (error) {
    console.error('❌ Error processing static data:', error)
    await resetTreeToInitialized(treeId)
    return NextResponse.json(
      { error: 'Failed to process static content' },
      { status: 500 }
    )
  }
}

/**
 * Generate content using AI streaming with enhanced system prompt
 */
async function generateContentWithAI(dragTree: any, userId: string) {
  console.log('🤖 [Drag Tree Generate] Using centralized model configuration')

  const systemPrompt = buildEnhancedSystemPrompt(dragTree)

  // Get model configuration based on user's subscription tier
  const modelConfig = await getModelConfig(
    userId,
    'dragtree_generate_questions'
  )
  const modelName = modelConfig.model

  // Use Vercel AI SDK streamText with onFinish callback
  const result = await streamText({
    model: azure(modelName),
    messages: [{ role: 'system', content: systemPrompt }],
    temperature: modelConfig.temperature,
    maxTokens: modelConfig.maxTokens,
    onFinish: async result => {
      try {
        console.log(
          '✅ [Drag Tree Generate] Streaming completed, processing final structure'
        )
        const finalMarkdown = result.text

        // Process markdown and log AI usage in parallel
        await Promise.all([
          processGeneratedMarkdown(dragTree.id, finalMarkdown),
          createAIUsage({
            userId: userId,
            entityType: 'drag_tree',
            entityId: dragTree.id,
            aiProvider: 'azure_openai',
            modelName: modelName,
            usageType: AIUsageType.GENERATE_QUESTION,
            inputPrompt: systemPrompt,
            messages: [
              { role: 'system', content: systemPrompt },
              ...(result.response.messages || []),
            ],
            metadata: {
              tokenUsage: result.usage,
              language: dragTree.preferred_language,
              hasWebSearch: false,
            },
            config: {},
          }).catch(error => {
            // Log error but don't fail the main request
            console.error('📊 AI usage logging failed:', error)
          }),
        ])

        console.log(
          '🎉 [Drag Tree Generate] Successfully completed generation and DB update'
        )
      } catch (_error) {
        console.error('💥 [Drag Tree Generate] Error in onFinish:', _error)
        await resetTreeToInitialized(dragTree.id)
      }
    },
  })

  return result.toDataStreamResponse()
}

/**
 * Build enhanced system prompt incorporating metadata and problem analysis
 */
function buildEnhancedSystemPrompt(dragTree: DragTree): string {
  const metadata = (dragTree.metadata as DragTreeMetadata) || {}
  const problemAnalysis = metadata.problemAnalysisItems || {}
  const language = getLanguageName(
    dragTree.preferred_language as SupportedLanguageCode
  )
  // ref: https://chatgpt.com/share/6857087e-c70c-8003-bee5-5130f99c4a0b

  // Enhanced system prompt with MECE structure and spark questions
  let systemPrompt = `You are a senior strategy consultant who crafts exhaustive, MECE* issue-trees that feel fresh, spark debate, and are packed with search-ready hooks.
*MECE = Mutually Exclusive, Collectively Exhaustive

TASK
Inside { … } you will receive a project brief.
Produce a multi-layer **Markdown** issue tree.

OUTPUT FORMAT
- Exactly **one** level-1 heading (#) that captures the brief in 3-8 words.
- Headings only (##, ###, ####, …) — no bullets, numbers, or commentary.

STRUCTURE BASICS
- ≥ 6 top-level categories (##).
- Each top-level → ≥ 2 sub-categories (###).
- Each sub-category → ≥ 3 leaf questions (####).
- Leaves are full-sentence, open-ended, and end with "?".
- Asymmetry welcome—go deep where insight is rich, stay lean where it is not.
- Only leaf headings may be written as questions.
- Every non-leaf heading (any level that still has children beneath it) must be a noun or gerund phrase, never a question.

CONTENT RICHNESS RULES
Every sub-category aim to include at least one of attention-grabbing questions:
1. **Root cause** ("Why…?", "Under what conditions…?")
2. **Metric / timeframe** ("How many…?", "By when…?", "To what extent…?")
3. **Scenario / second-order** ("If X happens, what then…?", "Suppose Y doubles…")

SPARK QUESTION RULE
• For every sub-category, weave in **at least one** attention-grabbing question.
• Rotate patterns: *do not use the same spark pattern in two adjacent sub-categories.*
• Pick any pattern below (or invent an equivalent) and blend it in naturally.
• Do **not** label the question; let it stand on its own.

1. Extreme / counter-factual
     – "If tomorrow all remote work were banned, how would X adapt?"
     – "Suppose interest rates jump 500 bp overnight—what breaks first?"

2. Personal trade-off
     – "Which single feature would you keep if budget cuts halve scope?"
     – "If you could secure only one visa type, which pathway wins?"

3. Concrete image or time box
     – "With just one 10-minute phone call, what insight must you confirm?"
     – "Design a 24-hour rescue plan—what step happens at hour 3?"

4. Debate-worthy threshold
     – "When adoption drops below 40 %, do we pivot or persist?"
     – "If churn exceeds 5 % per month, who owns the fix?"

5. Playful analogy / game mechanic
     – "Treat next quarter like a chess opening—what is the compulsory first move?"
     – "If this rollout were an RPG quest, which 'boss fight' defines success?"

*Reuse the pattern, not the exact phrasing, and aim for different metaphors across the tree.*

ENGAGEMENT BOOSTERS
- **Shuffle** the order of root cause, metric, scenario, and spark within each sub-category; avoid predictable sequences.
- Across every top-level, use **≥ 5 different interrogatives** (Why / Who / When / Which / Where / How-many / To-what-extent…).
- Weave in concrete nouns and proper nouns (tech names, KPIs, policy terms) so each leaf can anchor a web search.
- Aim for ≥ 1 numeric KPI or proper noun per sub-category (eg: EBITDA, ROE, license counts, fraud per 1 000, valuation, etc.)
- Avoid starting more than two consecutive leaves with the same interrogative.

DIVERSITY GUARDRAILS
- If the brief names ≥ 2 distinct entities (people, orgs, tech, concepts, timing, etc.), add ≥ 1 question about their interaction or synergy.
- Skip duplicates (~80 % similarity).
- If the brief mentions a date or cycle, aim to include a timing-specific leaf related to general macro environment.

STYLE GUARDRAILS
- Headings only; no extra prose.
- Respond deterministically; avoid off-topic creativity.
- No tags like "(Spark)" or "(New perspective)"; just write the category/question
- Maintain a confident, neutral, and professional tone as a consultant.

THINK FIRST (do **not** output)
1. Brain-map ideas to: root causes, metrics, scenarios, spark triggers, synergies, timing or other suitable dimensions, and their interactions.
2. Shuffle leaf order; track interrogative variety.
3. Draft ≥ 1 interaction/synergy leaf if multiple entities exist.
4. Glance over total length; keep it concise enough to read at a glance, but strict line limits are **not required**.
5. Render the Markdown tree.
`

  // Add problem analysis context if available
  const analysisContext = buildProblemAnalysisContext(problemAnalysis)
  if (analysisContext) {
    systemPrompt += `

BACKGROUND CONTEXTS  (for the model only)
The following analysis has been performed on the user's request:

${analysisContext}

Use this analysis to understand the user's true intentions and focus on the most relevant categories and questions`
  }

  systemPrompt += `

### INTERNAL THINKING (do not print)
1. Craft a concise, descriptive project title (3–8 words) for the # heading.
2. Brain-dump lenses (Why, What, How, When, Who) plus cross-disciplinary angles.
3. Draft branches; verify diversity rule for each.
4. Add one original, off-beat top-level category that still fits the brief.
5. Convert to headings and output—nothing else.

Ensure that there's only ONE level-one heading (#) in the entire output and that it's at the beginning.

The user asks: {
${dragTree.user_prompt}
}

Review the BACKGROUND CONTEXT and their ask. The user may ask weird things, do your best to adopt the format exactly to design that, **you have to output the issue tree regardless of the situation**

Please output in ${language}`

  return systemPrompt
}

/**
 * Build problem analysis context from metadata
 */
function buildProblemAnalysisContext(
  problemAnalysis: DragTreeMetadata['problemAnalysisItems']
): string {
  if (!problemAnalysis) return ''

  const contextParts: string[] = []

  // Add identified issues
  if (
    problemAnalysis.identified_issue &&
    problemAnalysis.identified_issue.length > 0
  ) {
    contextParts.push(`**Key Issues Identified:**
${problemAnalysis.identified_issue.map(issue => `- ${issue}`).join('\n')}`)
  }

  // Add identified intentions
  if (
    problemAnalysis.identified_intention &&
    problemAnalysis.identified_intention.length > 0
  ) {
    contextParts.push(`**User Intentions:**
${problemAnalysis.identified_intention.map(intention => `- ${intention}`).join('\n')}`)
  }

  // Add identified entities
  if (
    problemAnalysis.identified_entities &&
    problemAnalysis.identified_entities.length > 0
  ) {
    contextParts.push(`**Key Entities/Stakeholders:**
${problemAnalysis.identified_entities.map(entity => `- ${entity}`).join('\n')}`)
  }

  return contextParts.join('\n\n')
}

/**
 * Handle unexpected errors
 */
async function handleUnexpectedError(
  dragTreeId?: string
): Promise<NextResponse> {
  // Try to reset tree status if we have dragTreeId
  if (dragTreeId) {
    await resetTreeToInitialized(dragTreeId)
  }

  return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
}

/**
 * Process the generated markdown and update database
 */
async function processGeneratedMarkdown(
  dragTreeId: string,
  finalMarkdown: string
) {
  console.log('📝 [Process Markdown] Starting to process generated content')

  try {
    // Parse markdown to tree structure
    const treeNode = markdownToTreeNode(finalMarkdown)
    console.log('🔄 [Process Markdown] Successfully parsed markdown to tree')

    // Convert to hierarchy format
    const { root_id, hierarchy } = convertTreeNodeToHierarchy(
      dragTreeId,
      treeNode
    )

    // Collect all nodes for bulk insert
    const allNodes = collectAllNodesFromStructure(dragTreeId, treeNode)

    console.log(
      `📊 [Process Markdown] Created ${allNodes.length} nodes with root: ${root_id}`
    )

    // Create all nodes using batch operation
    await prisma.dragTreeNode.createMany({
      data: allNodes,
      skipDuplicates: true,
    })

    // Extract title from root node for better UX
    const rootNodeTitle = treeNode?.label || 'Drag Tree Analysis'

    // Update drag tree with structure, status, and proper title
    const updateResult = await updateDragTree({
      treeId: dragTreeId,
      title: rootNodeTitle, // Use root node label as title
      status: DragTreeStatus.ACTIVE,
      rawMarkdown: finalMarkdown,
      treeStructure: { root_id, hierarchy },
    })

    if (!updateResult.success) {
      throw new Error(updateResult.error || 'Failed to update drag tree')
    }

    console.log(
      '✅ [Process Markdown] Successfully updated drag tree to ACTIVE'
    )
  } catch (error) {
    console.error('❌ [Process Markdown] Failed to process markdown:', error)
    throw error
  }
}

/**
 * Reset tree status to INITIALIZED on error
 */
async function resetTreeToInitialized(dragTreeId: string) {
  try {
    console.log(`🔄 [Reset Status] Resetting tree ${dragTreeId} to INITIALIZED`)
    await updateDragTree({
      treeId: dragTreeId,
      status: DragTreeStatus.INITIALIZED,
    })
  } catch (error) {
    console.error('❌ [Reset Status] Failed to reset tree status:', error)
  }
}

/**
 * Convert tree node to hierarchy format for database storage
 */
function convertTreeNodeToHierarchy(
  dragTreeId: string,
  treeNode: any
): {
  root_id: string
  hierarchy: Record<string, string[]>
} {
  const hierarchy: Record<string, string[]> = {}

  function buildHierarchy(node: any): string {
    // Use the node.type that was correctly assigned by markdownToTreeNode
    const nodeType = node.type === 'question' ? 'QUESTION' : 'CATEGORY'
    const nodeId = generateDragTreeNodeId(dragTreeId, node.label, nodeType)

    if (node.children && node.children.length > 0) {
      const childrenIds = node.children.map((child: any) =>
        buildHierarchy(child)
      )
      hierarchy[nodeId] = childrenIds
    } else {
      hierarchy[nodeId] = []
    }

    return nodeId
  }

  const root_id = buildHierarchy(treeNode)
  return { root_id, hierarchy }
}

/**
 * Collect all nodes from tree structure for database insertion
 */
function collectAllNodesFromStructure(
  dragTreeId: string,
  treeNode: any,
  _parentId?: string // Currently unused in this function
): Array<{
  id: string
  drag_tree_id: string
  node_type: 'CATEGORY' | 'QUESTION'
  label: string
  metadata: any
  status: DragTreeNodeStatus
}> {
  const allNodes: ReturnType<typeof collectAllNodesFromStructure> = []

  function collectRecursive(node: any, currentParentId?: string): void {
    if (!node) return

    // The node.type is now correctly assigned by markdownToTreeNode
    const nodeType =
      node.type === 'question' ? ('QUESTION' as const) : ('CATEGORY' as const)
    const nodeId = generateDragTreeNodeId(dragTreeId, node.label, nodeType)

    allNodes.push({
      id: nodeId,
      drag_tree_id: dragTreeId,
      node_type: nodeType,
      label: node.label.trim(),
      metadata: {
        parent_id: currentParentId,
      },
      status: DragTreeNodeStatus.ACTIVE,
    })

    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        collectRecursive(child, nodeId)
      }
    }
  }

  collectRecursive(treeNode)
  return allNodes
}
