import { renderHook, act } from '@testing-library/react'
import { useScreeningDiagnosis } from '@/app/(conv)/screening/hooks/useScreeningDiagnosis'
import { experimental_useObject as useObject } from 'ai/react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'

// Mock dependencies
jest.mock('ai/react', () => ({
  experimental_useObject: jest.fn(),
}))
jest.mock('next-auth/react')
jest.mock('react-hot-toast')

const useObjectMock = useObject as jest.Mock
const useSessionMock = useSession as jest.Mock

describe('useScreeningDiagnosis', () => {
  let mockSubmit: jest.Mock
  let onScreenObjectUpdate: jest.Mock

  beforeEach(() => {
    jest.clearAllMocks()
    mockSubmit = jest.fn()
    onScreenObjectUpdate = jest.fn()

    useObjectMock.mockReturnValue({
      object: null,
      submit: mockSubmit,
      isLoading: false,
    })
    useSessionMock.mockReturnValue({ data: { user: { id: 'test-user' } } })
  })

  it('should call submit with the correct data', () => {
    const { result } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )
    const testData = {
      description: 'test description',
      preferredLanguage: 'en' as const,
      userId: 'test-user-id',
    }

    act(() => {
      result.current.submit(testData)
    })

    expect(mockSubmit).toHaveBeenCalledWith(testData)
  })

  it('should show an error if user is not authenticated', () => {
    useSessionMock.mockReturnValue({ data: null })
    const { result } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )
    const testData = {
      description: 'test description',
      preferredLanguage: 'en' as const,
      userId: 'test-user-id',
    }

    act(() => {
      result.current.submit(testData)
    })

    expect(toast.error).toHaveBeenCalledWith(
      'Please log in to use the real API.'
    )
    expect(mockSubmit).not.toHaveBeenCalled()
  })

  it('should call onScreenObjectUpdate when the object result changes', () => {
    const { rerender } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )

    const newObject = { problem_ambiguity: 'High' }
    useObjectMock.mockReturnValue({
      object: newObject,
      submit: mockSubmit,
      isLoading: false,
    })

    rerender({})

    expect(onScreenObjectUpdate).toHaveBeenCalledWith(newObject)
  })

  it('should map isLoading to isStreaming', () => {
    useObjectMock.mockReturnValue({
      object: null,
      submit: mockSubmit,
      isLoading: true,
    })
    const { result } = renderHook(() =>
      useScreeningDiagnosis({ onScreenObjectUpdate })
    )
    expect(result.current.isStreaming).toBe(true)
  })
})
